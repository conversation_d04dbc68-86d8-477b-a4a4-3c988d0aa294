/**
 * Props interface for CloseAccountButtons component
 * Handles different states and actions for account closure process
 */
export interface CloseAccountButtonsProps {
  /** Whether the account has been reactivated */
  reactivatedAccount?: boolean
  
  /** Whether the user has confirmed they want to close their account */
  confirmClosing?: boolean
  
  /** Function to set the confirmation state */
  setConfirmClosing?: (confirmClosing: boolean) => void
  
  /** Scheduled time for account closure (if any) */
  scheduledClosingTime?: unknown
  
  /** Function to set the reactivated account state */
  setReactivatedAccount?: (reactivated: boolean) => void
  
  /** Callback function when close account button is clicked */
  onClickCloseAccount?: () => void
}
