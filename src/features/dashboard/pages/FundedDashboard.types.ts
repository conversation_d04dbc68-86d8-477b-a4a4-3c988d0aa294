import { IncomeForecastParams } from '../../../common/types/CommonTypes.types'
import { ErrorStorage } from '../../../features/CommonState.type'

type PlanToDraw = {
  data?: unknown
  plan_id?: string
}

type DraftPlan = {
  plan_id?: string
}

type FundedDashboardProps = {
  data: IncomeForecastParams
  error?: ErrorStorage
  isLoading?: boolean
}

export type { DraftPlan, FundedDashboardProps, PlanToDraw }
