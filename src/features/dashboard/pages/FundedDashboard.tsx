import Button from '../../../common/components/Button'
import DividerHeader from '../../../common/components/DividerHeader'
import ErrorBoundaryAndSuspense from '../../../common/components/ErrorBoundaryAndSuspense'
import Icon from '../../../common/components/Icon'
import LottieAnimation from '../../../common/components/LottieAnimation'
import TextError from '../../../common/components/TextError'
import { ANIMATION } from '../../../common/constants/Animations'
import { ASSET } from '../../../common/constants/Assets'
import { useCustomNavigation } from '../../../common/hooks/useCustomNavigation'
import { useTranslate } from '../../../common/hooks/useTranslate'
import { DASHBOARD_NAVIGATION } from '../../../routes/Route'
import { useAccountService } from '../../authentication/hooks/useAccountService'
import NextPayoutCard from '../components/NextPayoutCard'
import PensionPlanDashboard from '../components/PensionPlanDashboard'
import style from '../style/ProgressDashboard.module.scss'
import type { FundedDashboardProps } from './FundedDashboard.types'

const FundedDashboard = ({ data, error, isLoading }: FundedDashboardProps) => {
  const t = useTranslate()
  const navigate = useCustomNavigation()
  const {
    context: { user_details },
  } = useAccountService()

  if (isLoading) {
    return (
      <LottieAnimation
        loop
        autoplay
        animationName={ANIMATION.loadingLightBlueDots}
      />
    )
  }

  return (
    <>
      {user_details?.kyc_status?.L2?.passed_level && (
        <NextPayoutCard className={style[`progress-dashboard__next-payout`]} />
      )}

      <DividerHeader
        headerText={t('DASHBOARD.CHART.TITLE.EXPECTED.MONTHLY.INCOME')}
      />
      {error && <TextError errorText={error.translatedError} />}

      {!isLoading && (
        <>
          <div className={style[`progress-dashboard__animation-container`]}>
            <Icon
              fileName={ASSET.dashboardPlaceholder}
              className={style[`progress-dashboard__animation`]}
            />
          </div>
          <section className={style[`progress-dashboard__button-container`]}>
            <Button
              className={style[`progress-dashboard__button`]}
              variant="primary"
              onClick={() => navigate(DASHBOARD_NAVIGATION.TONTINATOR)}
            >
              {t('CRATE_PLAN_BUTTON')}
            </Button>
          </section>
        </>
      )}

      {!isLoading && (
        <ErrorBoundaryAndSuspense>
          <PensionPlanDashboard
            dataToDraw={[data]}
            className={style[`progress-dashboard__chart`]}
          />
        </ErrorBoundaryAndSuspense>
      )}
    </>
  )
}

export default FundedDashboard
